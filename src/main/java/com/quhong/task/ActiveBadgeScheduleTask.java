package com.quhong.task;

import com.quhong.constant.ActiveBadgeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.redis.ActiveBadgeRedis;
import com.quhong.service.ActiveBadgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * 活跃勋章定时任务
 */
@Component
public class ActiveBadgeScheduleTask {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeScheduleTask.class);

    @Resource
    private ActiveBadgeService activeBadgeService;

    @Resource
    private ActiveBadgeRedis activeBadgeRedis;

    /**
     * 每天沙特时间20:00推送活跃提醒消息
     * 0 0 20 * * ? 表示每天20:00:00执行
     */
    @Scheduled(cron = "0 0 20 * * ?", zone = "Asia/Riyadh")
    public void sendDailyActiveReminder() {
        logger.info("开始执行每日活跃提醒推送任务");
        
        try {
            // 获取当前沙特时间
            ZonedDateTime saudiTime = ZonedDateTime.now(ZoneId.of("Asia/Riyadh"));
            logger.info("当前沙特时间: {}", saudiTime);
            
            // 检查是否为推送时间
            if (saudiTime.getHour() == ActiveBadgeConstant.PUSH_HOUR) {
                // 这里可以添加推送逻辑
                // 例如：获取今日未活跃的用户列表，发送推送消息
                logger.info("执行活跃提醒推送逻辑");
                
                // 示例推送逻辑（需要根据实际推送系统实现）
                // Set<String> inactiveUsers = activeBadgeRedis.getTodayInactiveUsers();
                // if (inactiveUsers != null && !inactiveUsers.isEmpty()) {
                //     for (String uid : inactiveUsers) {
                //         sendActiveReminderMessage(uid);
                //     }
                // }
            }
            
            logger.info("每日活跃提醒推送任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行每日活跃提醒推送任务失败", e);
        }
    }

    /**
     * 每天凌晨清理过期的Redis数据（可选）
     * 0 0 1 * * ? 表示每天凌晨1:00执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupExpiredData() {
        logger.info("开始执行Redis数据清理任务");
        
        try {
            // 可以在这里添加清理过期数据的逻辑
            // 例如清理超过7天的麦上时长记录等
            
            logger.info("Redis数据清理任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行Redis数据清理任务失败", e);
        }
    }

    /**
     * 发送活跃提醒消息（示例方法）
     */
    private void sendActiveReminderMessage(String uid) {
        try {
            // 这里需要根据实际的推送系统实现
            // 例如：调用推送服务发送消息
            logger.info("发送活跃提醒消息给用户: {}", uid);
            
            // 示例消息内容
            String message = "亲爱的用户，今天还没有上麦哦！快来参与活动，获取活跃勋章吧！";
            
            // 调用推送服务
            // pushService.sendMessage(uid, "活跃提醒", message);
            
        } catch (Exception e) {
            logger.error("发送活跃提醒消息失败. uid={}", uid, e);
        }
    }
}

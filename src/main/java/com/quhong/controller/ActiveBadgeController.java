package com.quhong.controller;

import com.quhong.data.vo.ActiveBadgeVO;
import com.quhong.enums.ApiResult;
import com.quhong.service.ActiveBadgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活跃勋章控制器
 */
@RestController
@RequestMapping("/active_badge")
public class ActiveBadgeController {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeController.class);

    @Resource
    private ActiveBadgeService activeBadgeService;

    /**
     * 获取用户活跃勋章信息
     */
    @PostMapping("/info")
    public ApiResult<ActiveBadgeVO> getActiveBadgeInfo(@RequestParam String uid) {
        try {
            if (uid == null || uid.trim().isEmpty()) {
                return ApiResult.getError("用户ID不能为空");
            }
            
            ActiveBadgeVO result = activeBadgeService.getActiveBadgeInfo(uid);
            return ApiResult.getSuccess(result);
        } catch (Exception e) {
            logger.error("get active badge info error. uid={}", uid, e);
            return ApiResult.getError("获取活跃勋章信息失败");
        }
    }
}

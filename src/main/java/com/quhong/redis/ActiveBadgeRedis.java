package com.quhong.redis;

import com.quhong.constant.ActiveBadgeConstant;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 活跃勋章Redis操作类
 */
@Component
public class ActiveBadgeRedis {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * 用户累积活跃天数Key
     */
    private static final String USER_ACCUMULATE_DAYS_KEY = "active_badge:accumulate_days:";
    
    /**
     * 用户当日麦上时长Key
     */
    private static final String USER_DAILY_MIC_TIME_KEY = "active_badge:daily_mic_time:";
    
    /**
     * 用户最后活跃日期Key
     */
    private static final String USER_LAST_ACTIVE_DATE_KEY = "active_badge:last_active_date:";
    
    /**
     * 用户勋章解锁记录Key
     */
    private static final String USER_BADGE_UNLOCK_KEY = "active_badge:badge_unlock:";
    
    /**
     * 获取用户累积活跃天数
     */
    public int getUserAccumulateDays(String uid) {
        String key = USER_ACCUMULATE_DAYS_KEY + uid;
        String value = stringRedisTemplate.opsForValue().get(key);
        return value != null ? Integer.parseInt(value) : 0;
    }
    
    /**
     * 设置用户累积活跃天数
     */
    public void setUserAccumulateDays(String uid, int days) {
        String key = USER_ACCUMULATE_DAYS_KEY + uid;
        stringRedisTemplate.opsForValue().set(key, String.valueOf(days));
        // 设置过期时间为180天
        stringRedisTemplate.expire(key, 180, TimeUnit.DAYS);
    }
    
    /**
     * 获取用户当日麦上时长（分钟）
     */
    public int getUserDailyMicTime(String uid, String dateStr) {
        String key = USER_DAILY_MIC_TIME_KEY + uid + ":" + dateStr;
        String value = stringRedisTemplate.opsForValue().get(key);
        return value != null ? Integer.parseInt(value) : 0;
    }
    
    /**
     * 增加用户当日麦上时长
     */
    public void addUserDailyMicTime(String uid, String dateStr, int minutes) {
        String key = USER_DAILY_MIC_TIME_KEY + uid + ":" + dateStr;
        stringRedisTemplate.opsForValue().increment(key, minutes);
        // 设置过期时间为7天
        stringRedisTemplate.expire(key, 7, TimeUnit.DAYS);
    }
    
    /**
     * 获取用户最后活跃日期
     */
    public String getUserLastActiveDate(String uid) {
        String key = USER_LAST_ACTIVE_DATE_KEY + uid;
        return stringRedisTemplate.opsForValue().get(key);
    }
    
    /**
     * 设置用户最后活跃日期
     */
    public void setUserLastActiveDate(String uid, String dateStr) {
        String key = USER_LAST_ACTIVE_DATE_KEY + uid;
        stringRedisTemplate.opsForValue().set(key, dateStr);
        // 设置过期时间为180天
        stringRedisTemplate.expire(key, 180, TimeUnit.DAYS);
    }
    
    /**
     * 检查用户是否已解锁指定勋章
     */
    public boolean isUserBadgeUnlocked(String uid, int badgeLevel) {
        String key = USER_BADGE_UNLOCK_KEY + uid;
        String value = stringRedisTemplate.opsForHash().get(key, String.valueOf(badgeLevel));
        return "1".equals(value);
    }
    
    /**
     * 设置用户勋章解锁状态
     */
    public void setUserBadgeUnlock(String uid, int badgeLevel) {
        String key = USER_BADGE_UNLOCK_KEY + uid;
        stringRedisTemplate.opsForHash().put(key, String.valueOf(badgeLevel), "1");
        // 设置过期时间为180天
        stringRedisTemplate.expire(key, 180, TimeUnit.DAYS);
    }
    
    /**
     * 清除用户活跃数据（任务中断时使用）
     */
    public void clearUserActiveData(String uid) {
        setUserAccumulateDays(uid, 0);
        String key = USER_BADGE_UNLOCK_KEY + uid;
        stringRedisTemplate.delete(key);
    }
}
